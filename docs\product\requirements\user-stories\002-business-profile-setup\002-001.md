# Task 002-001: Create Business Profile Data Model

**Parent Use Case:** 002-business-profile-setup  
**Task ID:** 002-001  
**Title:** Create Business Profile Data Model  
**Estimated Development Time:** 1-2 hours (including tests/docs)  
**Estimated Agent Time:** 2-3 minutes  

## Description

Define and implement the business profile data structure in Supabase and create corresponding TypeScript interfaces. This forms the foundation for all business profile functionality, establishing the database schema, RLS policies, and type definitions that other components will use.

## Acceptance Criteria

- [ ] Business profile table exists in Supabase with all required fields
- [ ] RLS policies are implemented for multi-tenant data isolation
- [ ] TypeScript interfaces are defined and exported
- [ ] Database migration scripts are created and documented
- [ ] Table relationships are properly defined with foreign keys
- [ ] Data validation rules are implemented at the database level
- [ ] Unit tests validate data model constraints
- [ ] Documentation explains the data model structure and relationships

## Deliverables Checklist

### Database Schema
- [ ] Create `business_profiles` table in Supabase
- [ ] Add required columns: id, tenant_id, user_id, company_name, trading_name, etc.
- [ ] Implement proper data types and constraints
- [ ] Add created_at, updated_at timestamps
- [ ] Create indexes for performance optimization

### Security & Access Control
- [ ] Implement RLS policies for tenant isolation
- [ ] Create policy for users to read their own business profile
- [ ] Create policy for users to update their own business profile
- [ ] Test RLS policies with different user scenarios

### TypeScript Interfaces
- [ ] Define BusinessProfile interface with all fields
- [ ] Define CreateBusinessProfileRequest interface
- [ ] Define UpdateBusinessProfileRequest interface
- [ ] Export interfaces from shared types package

### Migration & Documentation
- [ ] Create database migration script
- [ ] Create rollback migration script
- [ ] Document table structure and field purposes
- [ ] Document RLS policy logic and security model

### Testing
- [ ] Write unit tests for TypeScript interface validation
- [ ] Test database constraints and validation rules
- [ ] Test RLS policies with multiple tenants
- [ ] Verify migration scripts work correctly

## Dependencies

- **Required Before Starting:**
  - 001-authentication-signup (user authentication system must exist)
  - Supabase project setup and configuration
  - Multi-tenant architecture foundation

- **Blocks These Tasks:**
  - 002-002 (Business Profile Form Component)
  - 002-004 (Logo Upload Component)
  - 002-008 (Business Profile API Endpoints)

## Technical Considerations

### Database Design
- Must include `tenant_id` for multi-tenancy support
- Consider future expansion fields (e.g., Companies House number)
- Optimize for mobile app query patterns
- Plan for offline sync requirements

### Security
- RLS policies must prevent cross-tenant data access
- Consider audit trail requirements for business data
- Validate all user inputs at database level

### Performance
- Index frequently queried fields (tenant_id, user_id)
- Consider read replica requirements for reporting
- Plan for horizontal scaling if needed

### UK Business Requirements
- Consider future integration with Companies House API
- Plan for UK-specific business data (VAT number, etc.)
- Consider professional body registration numbers

## Definition of Done

- [ ] Database table created and accessible via Supabase
- [ ] RLS policies tested and verified secure
- [ ] TypeScript interfaces compile without errors
- [ ] Migration scripts tested in development environment
- [ ] All unit tests pass
- [ ] Code review completed
- [ ] Documentation updated and reviewed
- [ ] Security review completed for RLS policies

## Notes

- This is a foundational task - take extra care with the data model design
- Consider future requirements but don't over-engineer
- Ensure the schema supports both online and offline usage patterns
- The business profile is central to report generation and branding
