// Types export for Strata Compliance core package

// Re-export auth types from Supabase
export type {
  AuthError,
  Session as AuthSession,
  User,
} from "@supabase/supabase-js";

// Role-based types
export type UserRole = "TenantAdmin" | "Scheduler" | "Inspector" | "ClientUser";
export type InspectionStatus =
  | "draft"
  | "scheduled"
  | "in_progress"
  | "completed"
  | "requires_follow_up"
  | "approved"
  | "cancelled";
export type EntityStatus = "active" | "inactive" | "archived";

// Core entity types based on our database schema
export interface Tenant {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  slug: string;
  logo_url?: string;
  subscription_tier: "free" | "pro" | "enterprise";
  max_users: number;
  max_clients: number;
  timezone: string;
  date_format: string;
  currency: string;
  status: EntityStatus;
  archived_at?: string;
  archived_by?: string;
}

export interface TenantUser {
  id: string;
  created_at: string;
  updated_at: string;
  tenant_id: string;
  user_id: string;
  role: UserRole;
  permissions: Record<string, boolean | string | number>;
  first_name?: string;
  last_name?: string;
  position?: string;
  office_phone?: string;
  mobile_phone?: string;
  signature_url?: string;
  profile_image_url?: string;
  status: "active" | "inactive" | "suspended";
  invited_at?: string;
  activated_at?: string;
  last_login_at?: string;
}

export interface Client {
  id: string;
  created_at: string;
  updated_at: string;
  tenant_id: string;
  name: string;
  url_safe_name?: string;
  logo_url?: string;
  client_type: number;
  emergency_contact_details?: string;
  escalation_procedure?: string;
  kpis_and_slas?: string;
  invoice_email_address?: string;
  account_queries_email_address?: string;
  general_requirements?: string;
  building_name?: string;
  building_number?: string;
  unit?: string;
  floor?: string;
  status: EntityStatus;
  archived_at?: string;
  archived_by?: string;
  archive_reason?: string;
}

export interface Site {
  id: string;
  created_at: string;
  updated_at: string;
  tenant_id: string;
  client_id: string;
  name: string;
  description?: string;
  site_code?: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state_province?: string;
  postal_code?: string;
  country: string;
  latitude?: number;
  longitude?: number;
  site_plan_url?: string;
  floor_plan_urls: string[];
  status: EntityStatus;
  archived_at?: string;
  archived_by?: string;
  archive_reason?: string;
}

export interface Property {
  id: string;
  created_at: string;
  updated_at: string;
  tenant_id: string;
  client_id: string;
  site_id?: string;
  property_code?: string;
  notes?: string;
  group_name?: string;
  building_name?: string;
  building_number?: string;
  unit?: string;
  floor?: string;
  image_container_name?: string;
  image_blob_name?: string;
  qr_code_id?: string;
  qr_code_document_id?: string;
  is_managed: boolean;
  status: EntityStatus;
  archived_at?: string;
  archived_by?: string;
  archive_reason?: string;
  deleted_at?: string;
}

export interface Inspection {
  id: string;
  created_at: string;
  updated_at: string;
  tenant_id: string;
  property_id: string;
  assigned_inspector_id?: string;
  project_id?: string;
  inspection_type: string;
  title: string;
  description?: string;
  scheduled_date?: string;
  due_date?: string;
  completed_at?: string;
  status: InspectionStatus;
  findings: Record<string, string | number | boolean>;
  photos: string[];
  documents: string[];
  signature_data?: Record<string, string | number>;
  follow_up_required: boolean;
  follow_up_date?: string;
  follow_up_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  approved_by?: string;
  approved_at?: string;
}

// Insert and Update types
export type TenantInsert = Omit<Tenant, "id" | "created_at" | "updated_at">;
export type TenantUpdate = Partial<TenantInsert>;

export type ClientInsert = Omit<Client, "id" | "created_at" | "updated_at">;
export type ClientUpdate = Partial<ClientInsert>;

export type SiteInsert = Omit<Site, "id" | "created_at" | "updated_at">;
export type SiteUpdate = Partial<SiteInsert>;

export type PropertyInsert = Omit<Property, "id" | "created_at" | "updated_at">;
export type PropertyUpdate = Partial<PropertyInsert>;

export type InspectionInsert = Omit<
  Inspection,
  "id" | "created_at" | "updated_at"
>;
export type InspectionUpdate = Partial<InspectionInsert>;
