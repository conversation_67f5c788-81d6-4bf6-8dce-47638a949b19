# Database Setup Guide

This guide will help you implement the multi-tenant database structure for Strata Compliance.

## Prerequisites

1. **Supabase Project**: Create a new Supabase project at [supabase.com](https://supabase.com)
2. **Environment Variables**: Set up your Supabase credentials in your project

## 1. Migration Setup

### Run Migrations in Order

Execute the SQL migration files in your Supabase SQL editor in this order:

```bash
1. database/migrations/001_create_tenants.sql
2. database/migrations/002_create_tenant_users.sql
3. database/migrations/003_create_clients.sql
4. database/migrations/004_create_properties.sql
5. database/migrations/005_create_sites_and_inspections.sql
```

### Alternative: Supabase CLI

If you have Supabase CLI installed:

```bash
# Initialize Supabase in your project
supabase init

# Link to your project
supabase link --project-ref YOUR_PROJECT_REF

# Generate migration files
cp database/migrations/* supabase/migrations/

# Apply migrations
supabase db push
```

## 2. Configure Authentication

### JWT Claims

Add custom JWT claims to include `tenant_id` and `role` in your Supabase project:

1. Go to **Authentication** > **Settings** in Supabase Dashboard
2. Add this hook for JWT claims:

```sql
CREATE OR REPLACE FUNCTION auth.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
AS $$
DECLARE
  claims jsonb;
  tenant_user_data jsonb;
BEGIN
  -- Get tenant and role info for the user
  SELECT to_jsonb(tu) INTO tenant_user_data
  FROM tenant_users tu
  WHERE tu.user_id = (event->>'user_id')::uuid
  AND tu.status = 'active'
  LIMIT 1;

  -- Set default claims
  claims := event->'claims';
  
  IF tenant_user_data IS NOT NULL THEN
    claims := claims || jsonb_build_object(
      'tenant_id', tenant_user_data->>'tenant_id',
      'role', tenant_user_data->>'role'
    );
  END IF;

  -- Return the modified event
  RETURN jsonb_set(event, '{claims}', claims);
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION auth.custom_access_token_hook TO supabase_auth_admin;
```

## 3. Set Up Row Level Security (RLS)

The migration files already include RLS policies, but verify they're enabled:

```sql
-- Check RLS is enabled on all tables
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('tenants', 'tenant_users', 'clients', 'sites', 'properties', 'inspections');
```

## 4. Initial Data Setup

### Create Your First Tenant

```sql
-- Insert your organization as the first tenant
INSERT INTO tenants (name, slug, subscription_tier) 
VALUES ('Your Company Name', 'your-company', 'pro');
```

### Create Admin User Relationship

```sql
-- Link your user account to the tenant with admin role
-- Replace YOUR_USER_ID with your actual Supabase user ID
INSERT INTO tenant_users (tenant_id, user_id, role, first_name, last_name, status)
VALUES (
  (SELECT id FROM tenants WHERE slug = 'your-company'),
  'YOUR_USER_ID',
  'TenantAdmin',
  'Your First Name',
  'Your Last Name',
  'active'
);
```

## 5. Integration with TypeScript

### Install Dependencies

Make sure your core package has the necessary dependencies:

```json
{
  "dependencies": {
    "@supabase/supabase-js": "^2.x.x"
  }
}
```

### Use in Your Application

```typescript
import { createClient } from '@supabase/supabase-js'
import { DatabaseService } from '@strata/core'

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!
)

const db = new DatabaseService(supabase)

// Example: Get clients for current tenant
const clients = await db.getClients(currentTenantId)
```

## 6. Testing the Setup

### Verify Multi-tenant Isolation

1. Create test data for different tenants
2. Verify RLS policies prevent cross-tenant data access
3. Test role-based permissions

```sql
-- Test query (should only return data for current user's tenant)
SELECT * FROM clients; -- Should respect RLS and only show current tenant's clients
```

## 7. Environment Configuration

### Supabase Environment Variables

Set these in your deployment environment:

```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key (for server-side operations)
```

### Development

For local development with Supabase CLI:

```env
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your-local-anon-key
```

## 8. Next Steps

1. **Test the migrations** in a development environment first
2. **Seed sample data** to test the relationships
3. **Implement authentication flow** in your apps
4. **Create API endpoints** using the DatabaseService
5. **Add validation** and business logic in the core package

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**: Ensure JWT contains `tenant_id` claim
2. **Permission Denied**: Check user has correct role in `tenant_users`
3. **Foreign Key Violations**: Ensure parent records exist before creating child records

### Useful Queries

```sql
-- Check user's tenant relationship
SELECT * FROM tenant_users WHERE user_id = auth.uid();

-- View current JWT claims
SELECT auth.jwt();

-- Check RLS policies
SELECT * FROM pg_policies WHERE schemaname = 'public';
``` 